/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, #2c5530, #4a7c59);
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #90EE90;
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 100px 20px 50px;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    margin-right: 2rem;
}

.hero-content h1 {
    font-size: 3rem;
    color: #2c5530;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.cta-button {
    background: linear-gradient(135deg, #4a7c59, #2c5530);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
}

.hero-image {
    flex: 1;
}

.hero-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Pollution Types Section */
.pollution-types {
    padding: 80px 0;
    background: #f8f9fa;
}

.pollution-types h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #2c5530;
    margin-bottom: 3rem;
}

.types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.type-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.type-card:hover {
    transform: translateY(-5px);
}

.type-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.type-card h3 {
    color: #2c5530;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* Effects Section */
.effects {
    padding: 80px 0;
    background: white;
}

.effects h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #2c5530;
    margin-bottom: 3rem;
}

.effects-content {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.effects-text {
    flex: 1;
}

.effects-text h3 {
    color: #d32f2f;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.effects-text ul {
    margin-bottom: 2rem;
    padding-left: 1.5rem;
}

.effects-text li {
    margin-bottom: 0.5rem;
    color: #666;
}

.effects-image {
    flex: 1;
}

.effects-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
}

/* Solutions Section */
.solutions {
    padding: 80px 0;
    background: #f8f9fa;
}

.solutions h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #2c5530;
    margin-bottom: 3rem;
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.solution-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.solution-card:hover {
    transform: translateY(-5px);
}

.solution-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.solution-card h3 {
    color: #4a7c59;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* Statistics Section */
.statistics {
    padding: 80px 0;
    background: linear-gradient(135deg, #2c5530, #4a7c59);
    color: white;
}

.statistics h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
}

.stat-item h3 {
    font-size: 3rem;
    color: #90EE90;
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 1.1rem;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: white;
    text-align: center;
}

.contact h2 {
    font-size: 2.5rem;
    color: #2c5530;
    margin-bottom: 1rem;
}

.contact > p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

.contact-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3rem;
}

.contact-info {
    text-align: left;
}

.contact-info h3 {
    color: #2c5530;
    margin-bottom: 1rem;
}

.contact-info p {
    margin-bottom: 0.5rem;
    color: #666;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    background: linear-gradient(135deg, #4a7c59, #2c5530);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
}

/* Footer */
footer {
    background: #2c5530;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-content {
        margin-right: 0;
        margin-bottom: 2rem;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .nav-links {
        gap: 1rem;
    }
    
    .effects-content {
        flex-direction: column;
    }
    
    .contact-content {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-info {
        text-align: center;
    }
}
